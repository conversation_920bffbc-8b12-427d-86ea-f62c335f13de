<script lang="ts">
	import type { IVideo } from '../../interfaces/video';
	import type { IVideoClip } from '../../interfaces/clip-time';
	import { generateSingleClipCommand, validateCommand, type CommandOptions } from '../../utils/yt-dlp-generator';
	
	export let video: IVideo;
	export let clip: IVideoClip | null = null; // 如果为null则生成批量命令
	export let isOpen: boolean = false;
	
	let command: string = '';
	let displayCommand: string = '';
	let copySuccess: boolean = false;
	let commandOptions: CommandOptions = {
		includeMetadata: true,
		includeSubtitles: false,
		writeAutoSubs: false,
		delogoRegions: video.delogoRegions || [],
		videoHeight: video.videoHeight
	};

	// 响应式更新commandOptions中的videoHeight
	$: if (video.videoHeight !== commandOptions.videoHeight) {
		commandOptions.videoHeight = video.videoHeight;
	}

	
	
	
	
	
	

	
	let isSelectingFolder = false;
	
	$: if (isOpen) {
		generateCommand();
	}
	
	function generateCommand() {
		try {
			const { includeMetadata, includeSubtitles, writeAutoSubs, delogoRegions, videoHeight } = commandOptions;

			const optionsToPass: CommandOptions = {
				includeMetadata,
				includeSubtitles,
				writeAutoSubs,
				delogoRegions: delogoRegions || video.delogoRegions || [],
				videoHeight: videoHeight || video.videoHeight
			};

			if (clip) {
				// 生成单个片段命令
				command = generateSingleClipCommand(video.id, clip, optionsToPass);
				displayCommand = command;
			} else {
				// 生成批量复制指令（为每个片段生成单独的命令）
				const commands = video.clips.map(clipItem =>
					generateSingleClipCommand(video.id, clipItem, optionsToPass)
				);
				command = commands.join('; '); // 复制时使用分号分隔
				displayCommand = commands.join('\n\n'); // 显示时使用换行分隔
			}
		} catch (error) {
			command = `错误: ${error.message}`;
			displayCommand = command;
		}
	}

	
	
	async function copyCommand() {
		try {
			await navigator.clipboard.writeText(command);
			copySuccess = true;
			setTimeout(() => {
				copySuccess = false;
			}, 2000);
		} catch (error) {
			console.error('复制失败:', error);
		}
	}
	
	function closeModal() {
		isOpen = false;
	}
	
	// 验证命令
	$: validation = validateCommand(command);
</script>

{#if isOpen}
	<!-- 模态框背景 -->
	<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" on:click={closeModal}>
		<!-- 模态框内容 -->
		<div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full mx-4 overflow-y-auto" on:click|stopPropagation>
			<!-- 标题 -->
			<div class="flex items-center justify-between mb-4">
				<h3 class="text-lg font-semibold text-gray-900 dark:text-white">
					{clip ? '单片段' : '批量复制'}yt-dlp命令生成
				</h3>
				<button
					on:click={closeModal}
					class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>
			
			<!-- 视频信息 -->
			<div class="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded">
				<h4 class="font-medium text-gray-900 dark:text-white mb-1">{video.title}</h4>
				<p class="text-sm text-gray-600 dark:text-gray-400">
					视频ID: {video.id} |
					{clip ? '单个片段' : `${video.clips.length} 个独立命令`}
				</p>
			</div>
			
			<!-- 命令选项 -->
			<div class="mb-4 space-y-3">
				<h4 class="font-medium text-gray-900 dark:text-white">命令选项</h4>
				
				<div class="grid grid-cols-1 gap-4">
					<!-- 分辨率信息显示 -->
					{#if video.videoHeight}
						<div class="p-3 bg-blue-50 dark:bg-blue-900 rounded-md">
							<div class="text-sm text-blue-800 dark:text-blue-200">
								<strong>检测到视频分辨率:</strong> {video.videoWidth || '?'}×{video.videoHeight}
								<br>
								<strong>下载策略:</strong> 格式排序优先选择VP9和{video.videoHeight}p
								<br>
								<strong>格式选择:</strong> <code class="bg-blue-100 dark:bg-blue-800 px-1 rounded text-xs">-f "bestvideo+bestaudio"</code>
								<br>
								<strong>排序优先级:</strong> <code class="bg-blue-100 dark:bg-blue-800 px-1 rounded text-xs">-S "vcodec:vp9,height:{video.videoHeight}"</code>
							</div>
						</div>
					{/if}

					<!-- 元数据选项 -->
					<div class="flex items-center gap-2">
						<input
							type="checkbox"
							id="includeMetadata"
							bind:checked={commandOptions.includeMetadata}
							on:change={generateCommand}
							class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
						/>
						<label for="includeMetadata" class="text-sm text-gray-700 dark:text-gray-300">
							嵌入元数据 (如果出现"Function not implemented"错误可禁用)
						</label>
					</div>
				</div>

				

				<!-- 选项开关 -->
				<div class="flex flex-wrap gap-4">
					<label class="flex items-center">
						<input
							type="checkbox"
							bind:checked={commandOptions.includeMetadata}
							on:change={generateCommand}
							class="mr-2"
						>
						<span class="text-sm text-gray-700 dark:text-gray-300">包含元数据</span>
					</label>

					<label class="flex items-center">
						<input
							type="checkbox"
							bind:checked={commandOptions.includeSubtitles}
							on:change={generateCommand}
							class="mr-2"
						>
						<span class="text-sm text-gray-700 dark:text-gray-300">下载字幕</span>
					</label>

					{#if commandOptions.includeSubtitles}
						<label class="flex items-center">
							<input
								type="checkbox"
								bind:checked={commandOptions.writeAutoSubs}
								on:change={generateCommand}
								class="mr-2"
							>
							<span class="text-sm text-gray-700 dark:text-gray-300">下载自动字幕</span>
						</label>
					{/if}
				</div>

				<!-- Delogo 区域信息 -->
				<div>
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
						Delogo 区域
					</label>
					<div class="text-xs text-gray-500 dark:text-gray-400">
						{#if video.delogoRegions && video.delogoRegions.length > 0}
							已配置 {video.delogoRegions.filter(r => r.enabled).length}/{video.delogoRegions.length} 个去logo区域
						{:else}
							未配置去logo区域
						{/if}
					</div>
				</div>
			</div>
			
			<!-- 命令预览 -->
			<div class="mb-4">
				<div class="flex items-center justify-between mb-2">
					<h4 class="font-medium text-gray-900 dark:text-white">生成的命令</h4>
					<button
						on:click={copyCommand}
						class="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-sm font-medium transition-colors"
						class:bg-green-500={copySuccess}
						class:hover:bg-green-600={copySuccess}
					>
						{copySuccess ? '✓ 已复制' : '📋 复制命令'}
					</button>
				</div>
				
				<div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
					<pre>{displayCommand}</pre>
				</div>
			</div>
			
			<!-- 验证结果 -->
			{#if validation.errors.length > 0 || validation.warnings.length > 0}
				<div class="mb-4">
					{#if validation.errors.length > 0}
						<div class="mb-2">
							<h5 class="text-sm font-medium text-red-600 dark:text-red-400 mb-1">错误:</h5>
							<ul class="text-sm text-red-600 dark:text-red-400 list-disc list-inside">
								{#each validation.errors as error}
									<li>{error}</li>
								{/each}
							</ul>
						</div>
					{/if}
					
					{#if validation.warnings.length > 0}
						<div>
							<h5 class="text-sm font-medium text-yellow-600 dark:text-yellow-400 mb-1">警告:</h5>
							<ul class="text-sm text-yellow-600 dark:text-yellow-400 list-disc list-inside">
								{#each validation.warnings as warning}
									<li>{warning}</li>
								{/each}
							</ul>
						</div>
					{/if}
				</div>
			{/if}
			
			<!-- 使用说明 -->
			<div class="text-sm text-gray-600 dark:text-gray-400">
				<h5 class="font-medium mb-1">使用说明:</h5>
				<ol class="list-decimal list-inside space-y-1">
					<li>确保已安装 yt-dlp 和 FFmpeg（首次使用请参考下方安装指南）</li>
					<li>选择预设路径或点击"选择文件夹"按钮选择自定义路径</li>
					<li>复制上方命令到终端执行</li>
					<li>文件将下载到指定路径</li>
				</ol>

				<div class="mt-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
					<h6 class="font-medium text-blue-800 dark:text-blue-200 mb-1">首次使用安装指南:</h6>
					<div class="text-xs space-y-1">
						<p><strong>Windows:</strong></p>
						<code class="bg-gray-100 dark:bg-gray-800 px-1 rounded">winget install yt-dlp</code><br>
						<code class="bg-gray-100 dark:bg-gray-800 px-1 rounded">winget install FFmpeg</code>

						<p class="mt-2"><strong>Mac:</strong></p>
						<code class="bg-gray-100 dark:bg-gray-800 px-1 rounded">brew install yt-dlp ffmpeg</code><br>
						<span class="text-xs text-gray-500">（需要先安装 Homebrew: <code class="bg-gray-100 dark:bg-gray-800 px-1 rounded">/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"</code>）</span>
					</div>
				</div>

				<div class="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded">
					<h6 class="font-medium text-yellow-800 dark:text-yellow-200 mb-1">更新说明:</h6>
					<div class="text-xs space-y-1">
						<p>• <strong>质量选择已优化</strong>：MP4 格式现在使用最佳视频+音频组合</p>
						<p>• <strong>字幕选项</strong>：可选择下载字幕文件（--write-subs）</p>
						<p>• <strong>批量命名</strong>：使用"片段时长_日期_ID_上传者"格式</p>
					</div>
				</div>
			</div>
		</div>
	</div>
{/if}
